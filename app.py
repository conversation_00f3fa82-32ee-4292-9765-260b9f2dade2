import streamlit as st
import pandas as pd
import re
from io import BytesIO
import numpy as np
from datetime import datetime
import math

# Set page configuration and styling
st.set_page_config(page_title="Commission Calculator", layout="wide")

# Custom CSS for consistent styling
st.markdown("""
<style>
    .main {
        background-color: #f8f9fa;
    }
    .stButton>button {
        background-color: #4CAF50;
        color: white;
    }
    .stProgress .st-bo {
        background-color: #4CAF50;
    }
    div[data-testid="stFileUploader"] {
        border: 2px dashed #4CAF50;
        border-radius: 8px;
        padding: 20px;
    }
</style>
""", unsafe_allow_html=True)

# Page header with icon
st.title("💰 Commission Calculator")
st.markdown("---")

def normalize_company_name(name):
    """Standardize company name for comparison by removing extra spaces and converting to lowercase"""
    if pd.isna(name):
        return ""
    # Remove all extra spaces (including between words) and convert to lowercase
    return ' '.join(str(name).split()).lower()

def is_empty_or_direct(value):
    """Check if a value is effectively empty or represents a direct entry"""
    if pd.isna(value):
        return True
    normalized = normalize_company_name(value)
    return normalized == "" or normalized == "nan" or normalized == "direct"

def parse_indian_number(numstr):
    """Convert Indian number format (with commas) to float"""
    try:
        # First clean up the string
        numstr = str(numstr).strip()

        # Handle zero, empty, and special cases
        if not numstr or numstr.lower() in ('nan', '0', '-'):
            return 0

        # Handle scientific notation (e.g., 1.00E+05)
        if 'e' in numstr.lower():
            try:
                return float(numstr)
            except:
                pass

        # Check for Indian currency notations
        orig_lower = str(numstr).lower()
        if 'cr' in orig_lower:
            multiplier = 10000000  # 1 crore = 10^7
        elif 'l' in orig_lower or 'lac' in orig_lower:
            multiplier = 100000    # 1 lakh = 10^5
        else:
            multiplier = 1

        # Remove all non-numeric chars except commas and dots
        numstr = re.sub(r'[^\d,.]', '', numstr)

        # Handle Indian number format (e.g., "1,00,000")
        if ',' in numstr:
            parts = numstr.split(',')
            # Handle parts differently based on Indian format
            if len(parts[-1]) == 3:  # Last part should be 3 digits
                try:
                    if len(parts) == 2:
                        # Format: x,xxx
                        value = float(''.join(parts))
                    else:
                        # Format: x,xx,xxx or x,xx,xx,xxx etc
                        first = parts[0]
                        middle = ''.join(str(p).zfill(2) for p in parts[1:-1])
                        last = parts[-1].zfill(3)
                        value = float(f"{first}{middle}{last}")
                except:
                    value = float(re.sub(r'[^\d.]', '', numstr))
            else:
                # Not a valid Indian format, remove commas
                value = float(re.sub(r'[^\d.]', '', numstr))
        else:
            # No commas, just convert to float
            value = float(numstr) if numstr else 0

        return value * multiplier

    except Exception as e:
        return 0

def parse_slab_range(slab_str):
    """Parse slab range with specific handling for standard formats"""
    try:
        # Clean up the string and convert to lowercase for matching
        slab_str = slab_str.replace('–', '-').strip()
        slab_lower = slab_str.lower()

        # Handle standard formats
        try:
            # Case 1: "Upto X" format
            if 'upto' in slab_lower:
                match = re.search(r'(\d[\d,]*)', slab_str)
                if match:
                    upper = parse_indian_number(match.group(1))
                    return 0, upper

            # Case 2: Standard range format (e.g., "5,00,000 - 9,00,000")
            if '-' in slab_str:
                parts = [p.strip() for p in slab_str.split('-')]
                if len(parts) == 2:
                    lower = parse_indian_number(parts[0])
                    upper_str = parts[1].lower()

                    # Check for "and above" or similar
                    if 'above' in upper_str or 'inf' in upper_str:
                        return lower, math.inf
                    else:
                        upper = parse_indian_number(parts[1])
                        return lower, upper

            # Case 3: "X and above" format
            if 'above' in slab_lower:
                # Try to extract the number before "and above"
                match = re.search(r'(\d[\d,]*)', slab_str)
                if match:
                    lower = parse_indian_number(match.group(1))
                    return lower, math.inf

            # Case 4: Direct number (for Excel values)
            if slab_str.replace('.', '').replace(',', '').isdigit():
                val = parse_indian_number(slab_str)
                return val, val

            raise ValueError("Did not match any expected format")

        except Exception as e:
            return None, None

    except Exception as e:
        return None, None


def validate_excel_structure(xl):
    """Validate if the Excel file has required sheets and structure"""
    if len(xl.sheet_names) < 2:
        return False, "Excel file must contain both Input and Slab sheets"

    # Validate Input Sheet structure
    try:
        input_df = xl.parse(xl.sheet_names[0])
        # Convert all column names to lowercase for case-insensitive comparison
        actual_columns = {col.lower().strip() for col in input_df.columns}

        # Define required and optional columns
        required_columns = {
            'client name': ['client name', 'client', 'name', 'clientname', 'Client Name'],
            'series no': ['series no', 'series', 'series number', 'seriesno', 'Series No'],
            'face value': ['face value', 'facevalue', 'value', 'amount', 'Face Value'],
            'distributor': ['distributor', 'dist', 'distributor name', 'Distributor'],
            'introducer': ['introducer', 'intro', 'introducer name', 'Introducer']
        }

        optional_columns = {
            'product_name': ['product name', 'product', 'productname', 'Product Name'],
            'product_isin': ['product isin', 'isin', 'product isin number', 'Product ISIN'],
            'amount_invested': ['amount invested', 'invested amount', 'investment', 'Amount Invested'],
            'no_of_debts': ['no of debts', 'debts', 'number of debts', 'No. of Debts'],
            'client_invested_date': ['client invested date', 'invested date', 'investment date', 'Client Invested Date'],
            'total_tenure': ['total tenure', 'tenure', 'period', 'Total Tenure'],
            'maturity_date': ['maturity date', 'maturity', 'end date', 'Maturity Date'],
            'rm_name': ['rm name', 'relationship manager', 'rm', 'RM Name']
        }

        missing_cols = []
        for col, variations in required_columns.items():
            if not any(var in actual_columns for var in variations):
                missing_cols.append(col.title())

        if missing_cols:
            return False, f"Input sheet missing required columns: {', '.join(missing_cols)}\nFound columns: {', '.join(input_df.columns)}"

        # Store the actual column names for later use
        st.session_state['column_mappings'] = {
            'client_name': next(col for col in input_df.columns if col.lower().strip() in required_columns['client name']),
            'series_no': next(col for col in input_df.columns if col.lower().strip() in required_columns['series no']),
            'face_value': next(col for col in input_df.columns if col.lower().strip() in required_columns['face value']),
            'distributor': next(col for col in input_df.columns if col.lower().strip() in required_columns['distributor']),
            'introducer': next(col for col in input_df.columns if col.lower().strip() in required_columns['introducer'])
        }

        # Add optional columns if they exist in the input file
        for key, variations in optional_columns.items():
            try:
                st.session_state['column_mappings'][key] = next(col for col in input_df.columns if col.lower().strip() in variations)
            except StopIteration:
                st.session_state['column_mappings'][key] = None

    except Exception as e:
        return False, f"Error parsing Input sheet: {str(e)}"

    return True, "Validation successful"

def parse_slab_matrix(slab_df):
    """Parse slab matrix with the exact format in the file"""
    slab_info = {}
    all_slab_ranges = [] # To store all unique slab range strings for the dropdown
    try:
        # Clean and convert data
        slab_df = slab_df.fillna('')

        # Scan for series information
        series_info = []

        # Define the row where 'Distributor'/'Introducer' headers are expected
        role_row = 2

        # Iterate through columns to find series headers and their corresponding rate columns
        # We start from column 1 because column 0 is for ranges.
        # We check for a 'Series' header in row 0, and 'Distributor' in row 2 of the same column,
        # and 'Introducer' in row 2 of the next column.
        col_idx = 1 # Start checking from column 1
        while col_idx < len(slab_df.columns):
            series_header_val = str(slab_df.iloc[0, col_idx]).strip()
            dist_header_val = str(slab_df.iloc[role_row, col_idx]).strip().lower()

            intro_header_val = ''
            if col_idx + 1 < len(slab_df.columns):
                intro_header_val = str(slab_df.iloc[role_row, col_idx + 1]).strip().lower()


            # Condition to identify a new series block:
            # 1. Row 0 contains "Series"
            # 2. Row 2 in the same column contains "Distributor"
            # 3. Row 2 in the next column contains "Introducer"
            if series_header_val.startswith('Series') and 'distributor' in dist_header_val and 'introducer' in intro_header_val:
                series_name = series_header_val
                dist_col = col_idx
                intro_col = col_idx + 1 # Assumed to be the very next column

                # Now find the start row for ranges (e.g., 'Upto X' or a number)
                range_start_row = None
                # Start search from the row *after* the role row, up to a reasonable limit
                for r_idx in range(role_row + 1, min(len(slab_df), role_row + 10)): # Check next 10 rows
                    val = str(slab_df.iloc[r_idx, 0]).strip().lower() # Check first column for range values
                    if 'upto' in val or re.match(r'^\d', val) or 'above' in val: # Added 'above' for robustness
                        range_start_row = r_idx
                        break

                if range_start_row is None:
                    col_idx += 1 # Move to the next column to avoid infinite loop
                    continue

                series_info.append({
                    'series': series_name,
                    'series_num_key': re.search(r'(\d+)', series_name).group(1) if re.search(r'(\d+)', series_name) else series_name,
                    'dist_col': dist_col,
                    'intro_col': intro_col,
                    'start_row': range_start_row
                })
                col_idx += 2 # Skip both the 'Distributor' and 'Introducer' columns as they are now processed for this series
            else:
                col_idx += 1 # Move to the next column if no series block found starting here

        if not series_info:
            return {}, [] # Return empty slab info and empty ranges if no series found

        # Process each series using their own start rows and column mappings
        for info in series_info:
            series_num_key = info['series_num_key']

            slab_data_for_series = []

            # Read rows starting from the series' specific start row
            for i in range(info['start_row'], len(slab_df)):
                range_str = str(slab_df.iloc[i, 0]).strip()
                if not range_str or range_str.lower() in ('nan', '-', 'mobilization on face value (inr)'): # Add extra check for common header in first column
                    break # Stop if we hit an empty or header row

                # Parse rates carefully - could be percentage or decimal
                def parse_rate(val):
                    val_str = str(val).strip()
                    try:
                        if '%' in val_str:
                            return float(val_str.replace('%', '')) / 100
                        else:
                            num = float(val_str)
                            # If the number is already small enough to be a decimal rate (e.g., 0.0169), keep it
                            if abs(num) < 1.0: # Use abs for safety with negative rates (though unlikely)
                                return num
                            # Otherwise, assume it's a percentage (e.g., 1.69) and convert
                            return num / 100
                    except (ValueError, Exception):
                        return 0.0

                # Get and validate rates using series-specific column indices
                dist_val = str(slab_df.iloc[i, info['dist_col']]).strip()
                intro_val = str(slab_df.iloc[i, info['intro_col']]).strip()

                parsed_dist_rate = parse_rate(dist_val)
                parsed_intro_rate = parse_rate(intro_val)

                lower, upper = parse_slab_range(range_str)
                if lower is None or upper is None:
                    continue

                # Add the original slab string to the list of all unique slab ranges
                if range_str not in all_slab_ranges:
                    all_slab_ranges.append(range_str)

                # Only add if rates are valid numbers (not NaN from previous issues)
                if not pd.isna(parsed_dist_rate) and not pd.isna(parsed_intro_rate):
                    slab_data_for_series.append((lower, upper, parsed_dist_rate, parsed_intro_rate))
                else:
                    continue

            if slab_data_for_series:
                slab_info[series_num_key] = slab_data_for_series

    except Exception as e:
        return {}, []

    return slab_info, sorted(all_slab_ranges) # Return sorted list of ranges

def calculate_commissions(input_df, slab_info):
    """Calculate commissions with validation and error handling, including slab overrides."""
    commission_rows = []
    skipped_rows = []

    # Get column names from mappings
    distributor_col = st.session_state['column_mappings']['distributor']
    series_col = st.session_state['column_mappings']['series_no']
    face_value_col = st.session_state['column_mappings']['face_value']
    client_col = st.session_state['column_mappings']['client_name']
    introducer_col = st.session_state['column_mappings']['introducer']

    # Create normalized columns for grouping using the standardized function
    input_df['distributor_normalized'] = input_df[distributor_col].apply(normalize_company_name)
    input_df['introducer_normalized'] = input_df[introducer_col].apply(normalize_company_name)

    # Calculate total face value for each Distributor across ALL series using normalized names
    distributor_overall_totals = input_df.groupby('distributor_normalized')[face_value_col].sum().to_dict()
    
    # Process individual rows using group rates
    for idx, row in input_df.iterrows():
        try:
            # Use column mappings to access data
            series_full = str(row[series_col])
            series_key = series_full.replace("Series", "").strip() # Clean series number for slab lookup
            face_value = float(row[face_value_col]) if pd.notna(row[face_value_col]) else 0
            # Keep original values for display
            distributor = str(row[distributor_col])
            introducer = str(row[introducer_col])
            # Use normalized values for lookups
            distributor_normalized = normalize_company_name(distributor)
            introducer_normalized = normalize_company_name(introducer)

            # Initialize rate variables
            dist_rate = intro_rate = 0
            matched_slab_range_str = "No matching slab found"
            slab_found = False

            # --- Apply Slab Override Logic ---
            if distributor in st.session_state.slab_overrides:
                override_value = st.session_state.slab_overrides[distributor]
                if override_value == "ZERO":
                    dist_rate = 0.0
                    intro_rate = 0.0
                    matched_slab_range_str = "0% Commission Override"
                    slab_found = True
                else:
                    # Parse the overridden slab range
                    override_lower, override_upper = parse_slab_range(override_value)
                    if override_lower is not None and override_upper is not None:
                        # Find the rates for this specific series within the overridden slab range
                        if series_key in slab_info:
                            for lower, upper, d_rate, i_rate in slab_info[series_key]:
                                if lower == override_lower and upper == override_upper:
                                    dist_rate = d_rate
                                    intro_rate = i_rate
                                    matched_slab_range_str = override_value
                                    slab_found = True
                                    break
                        if not slab_found:
                            skipped_rows.append((idx, f"Rates not found for {series_full} in overridden slab '{override_value}' for distributor '{distributor}'"))
                            continue
                    else:
                        skipped_rows.append((idx, f"Invalid slab override format for distributor '{distributor}': '{override_value}'"))
                        continue
            else:
                # --- Normal Volume-Based Slab Calculation ---
                # Skip if no slab data for this series_key
                if not series_key or series_key not in slab_info:
                    skipped_rows.append((idx, f"Invalid series number or no rate found for {series_full}"))
                    continue

                distributor_overall_total_for_slab_lookup = distributor_overall_totals.get(distributor_normalized, 0)

                if slab_info:
                    # Assuming all series in slab_info have the same structure of ranges
                    first_series_key_in_slab_info = next(iter(slab_info))
                    generic_slab_ranges = [(lower, upper) for lower, upper, _, _ in slab_info[first_series_key_in_slab_info]]

                    matched_range_tuple = None
                    for lower, upper in generic_slab_ranges:
                        if lower <= distributor_overall_total_for_slab_lookup <= upper:
                            matched_range_tuple = (lower, upper)
                            matched_slab_range_str = f"₹{lower:,.0f} to {'∞' if upper == math.inf else f'₹{upper:,.0f}'}"
                            slab_found = True
                            break

                    if not slab_found:
                        skipped_rows.append((idx, f"No matching slab found for distributor '{distributor}' overall total ₹{distributor_overall_total_for_slab_lookup:,.2f}"))
                        continue

                    if series_key in slab_info:
                        for lower, upper, d_rate, i_rate in slab_info[series_key]:
                            if lower == matched_range_tuple[0] and upper == matched_range_tuple[1]: # Ensure we match the exact range
                                dist_rate = d_rate
                                intro_rate = i_rate
                                break
                        else:
                            skipped_rows.append((idx, f"Rates not found for {series_full} in overall slab for distributor '{distributor}'"))
                            continue
                    else:
                        skipped_rows.append((idx, f"Series '{series_key}' not in slab info for distributor '{distributor}'"))
                        continue
                else:
                    skipped_rows.append((idx, "Slab information is empty"))
                    continue


            # Calculate base commissions using the determined rates
            base_dist_rate = dist_rate
            dist_comm = face_value * dist_rate if not is_empty_or_direct(distributor) else 0
            intro_comm = face_value * intro_rate if not is_empty_or_direct(introducer) else 0

            # Add extra commission for distributor if configured
            if distributor in st.session_state.commission_overrides and not is_empty_or_direct(distributor):
                extra_rate = st.session_state.commission_overrides[distributor] / 100.0
                extra_comm = face_value * extra_rate
                dist_comm += extra_comm
                base_dist_rate += extra_rate

            # Add extra commission for introducer if configured
            if introducer in st.session_state.introducer_overrides and not is_empty_or_direct(introducer):
                intro_extra_rate = st.session_state.introducer_overrides[introducer] / 100.0
                intro_extra_comm = face_value * intro_extra_rate
                intro_comm += intro_extra_comm
                intro_rate += intro_extra_rate

            # Special validation for ABC company test cases (existing)
            is_abc_test = (
                str(row[client_col]).strip() == "ABC company Pvt Ltd" and
                face_value in [500000, 100000] and
                series_key in ['123', '129']
            )


            # Create base commission row with required fields
            commission_row = {
                "Series": series_full,
                "Face Value": face_value,
                "Client": row[client_col],
                "Distributor": "" if is_empty_or_direct(distributor) else distributor,  # Keep original case for display
                "Introducer": "" if is_empty_or_direct(introducer) else introducer,

                # Store as string with formatted number to prevent Excel from interpreting as percentage
                "Total Distributor Face Value (for slab)": f"{distributor_overall_totals.get(distributor_normalized, 0):,.0f}",
                "Slab Applied": matched_slab_range_str, # New column for slab applied
                "Distributor Rate": "" if is_empty_or_direct(distributor) else f"{base_dist_rate*100:.2f}%",
                "Distributor Commission": round(dist_comm, 2),
                "Introducer Rate": "" if is_empty_or_direct(introducer) else f"{intro_rate*100:.2f}%",
                "Introducer Commission": round(intro_comm, 2),
                "Total Commission": round(dist_comm + intro_comm, 2),
                # Add new columns for manual adjustments
                "Distributor Adjustment Amount": 0.0,
                "Distributor Adjustment Remarks": "",
                "Introducer Adjustment Amount": 0.0,
                "Introducer Adjustment Remarks": "",
                "Client Adjustment Amount": 0.0,
                "Client Adjustment Remarks": ""
            }

            # Initialize sets to track adjusted entities for each type within this calculation run
            # This ensures adjustments are applied only once per entity per calculation
            if 'adjusted_distributors' not in st.session_state:
                st.session_state.adjusted_distributors = set()
            if 'adjusted_introducers' not in st.session_state:
                st.session_state.adjusted_introducers = set()
            if 'adjusted_clients' not in st.session_state:
                st.session_state.adjusted_clients = set()


            # Apply Manual Adjustments Logic
            # Iterate through manual adjustments and apply them to the FIRST matching row
            for adjustment in st.session_state.manual_adjustments:
                adj_type = adjustment['type']
                adj_entity = adjustment['entity']
                adj_amount = adjustment['amount']
                adj_is_percentage = adjustment['is_percentage']
                adj_remarks = adjustment.get('remarks', '') # Get remarks, default to empty string
                adj_product = adjustment.get('product_name') # Get product filter for client adjustments

                # Check if the current row matches the adjustment criteria AND the entity hasn't been adjusted yet
                entity_match = False
                already_adjusted = False

                if adj_type == "Distributor" and distributor == adj_entity:
                    entity_match = True
                    if adj_entity in st.session_state.adjusted_distributors:
                        already_adjusted = True
                elif adj_type == "Introducer" and introducer == adj_entity:
                    entity_match = True
                    if adj_entity in st.session_state.adjusted_introducers:
                        already_adjusted = True
                elif adj_type == "Client" and str(row[client_col]) == adj_entity:
                    entity_match = True
                    # Check for Product Name filter if it exists for client adjustments
                    if adj_type == "Client" and adj_product and st.session_state['column_mappings'].get('product_name'):
                        product_name_col = st.session_state['column_mappings']['product_name']
                        if str(row[product_name_col]) != adj_product:
                            entity_match = False # Don't apply if product doesn't match
                    # Check if client (and product if filtered) has already been adjusted
                    client_key = f"{adj_entity}_{adj_product}" if adj_product else adj_entity
                    if client_key in st.session_state.adjusted_clients:
                        already_adjusted = True


                if entity_match and not already_adjusted:
                    # Calculate the actual adjustment value
                    if adj_is_percentage:
                        adjustment_value = face_value * (adj_amount / 100.0)
                    else:
                        adjustment_value = adj_amount

                    # Apply adjustment to the total commission for THIS row
                    commission_row["Total Commission"] += round(adjustment_value, 2)

                    # Record the adjustment details in the specific columns for THIS row
                    if adj_type == "Distributor":
                        commission_row["Distributor Adjustment Amount"] = round(adjustment_value, 2)
                        commission_row["Distributor Adjustment Remarks"] = adj_remarks
                        st.session_state.adjusted_distributors.add(adj_entity) # Mark as adjusted
                    elif adj_type == "Introducer":
                        commission_row["Introducer Adjustment Amount"] = round(adjustment_value, 2)
                        commission_row["Introducer Adjustment Remarks"] = adj_remarks
                        st.session_state.adjusted_introducers.add(adj_entity) # Mark as adjusted
                    elif adj_type == "Client":
                        commission_row["Client Adjustment Amount"] = round(adjustment_value, 2)
                        commission_row["Client Adjustment Remarks"] = adj_remarks
                        client_key = f"{adj_entity}_{adj_product}" if adj_product else adj_entity
                        st.session_state.adjusted_clients.add(client_key) # Mark as adjusted (including product filter if used)


            # Add optional fields if they exist
            optional_fields = {
                "Product Name": 'product_name',
                "Product ISIN": 'product_isin',
                "Amount Invested": 'amount_invested',
                "No. of Debts": 'no_of_debts',
                "Client Invested Date": 'client_invested_date',
                "Total Tenure": 'total_tenure',
                "Maturity Date": 'maturity_date',
                "RM Name": 'rm_name'
            }

            for display_name, mapping_key in optional_fields.items():
                if st.session_state['column_mappings'].get(mapping_key):
                    value = row[st.session_state['column_mappings'][mapping_key]]
                    if pd.isna(value) or str(value).strip().lower() in ["nan", ""]:
                        value = ""
                    elif mapping_key == 'amount_invested':
                        try:
                            value = float(value) if str(value).strip() != "" else 0
                        except (ValueError, TypeError):
                            value = 0
                    commission_row[display_name] = value

            commission_rows.append(commission_row)

        except Exception as e:
            # Just collect the error without displaying
            skipped_rows.append((idx, str(e)))
            continue

    if skipped_rows:
        total_skipped = len(skipped_rows)
        if total_skipped > 0:
            st.warning(f"⚠️ Skipped {total_skipped} invalid entries")

    return pd.DataFrame(commission_rows)

def generate_summary(commission_df):
    """Generate summary statistics"""
    summaries = {}

    # Distributor Summary
    dist_summary = commission_df.groupby('Distributor').agg({
        'Face Value': 'sum',
        'Distributor Commission': 'sum'
    }).round(2)
    dist_summary = dist_summary[dist_summary['Distributor Commission'] > 0]
    summaries['distributor'] = dist_summary

    # Introducer Summary with Distributor breakdown
    # First, get the basic introducer summary
    intro_base = commission_df.groupby('Introducer').agg({
        'Face Value': 'sum',
        'Introducer Commission': 'sum'
    }).round(2)
    intro_base = intro_base[intro_base['Introducer Commission'] > 0]
    
    # Create detailed breakdown for introducers
    intro_detail_df = commission_df[commission_df['Introducer Commission'] > 0].copy()
    intro_detail_df['Rate'] = intro_detail_df['Introducer Rate'].str.rstrip('%').astype(float) / 100
    
    # Sort by Introducer, Distributor, and Series for consistent ordering
    intro_detail_df = intro_detail_df.sort_values(['Introducer', 'Distributor', 'Series'])
    
    # Group by Introducer for the detailed breakdown
    intro_breakdown = {}
    for introducer in intro_detail_df['Introducer'].unique():
        intro_data = intro_detail_df[intro_detail_df['Introducer'] == introducer].copy()
        # Reset index to remove it from display
        intro_data.reset_index(drop=True, inplace=True)
        breakdown = {
            'details': intro_data[['Distributor', 'Face Value', 'Series', 'Introducer Rate', 'Introducer Commission']],
            'total': intro_data['Introducer Commission'].sum()
        }
        intro_breakdown[introducer] = breakdown
    
    # Combine both summaries
    summaries['introducer'] = {
        'base': intro_base,
        'breakdown': intro_breakdown
    }

    # Series Summary with Product Name
    series_summary = commission_df.groupby('Series').agg({
        'Product Name': lambda x: x.iloc[0] if 'Product Name' in commission_df.columns else '',
        'Face Value': 'sum',
        'Distributor Commission': 'sum',
        'Introducer Commission': 'sum',
        'Total Commission': 'sum'
    }).round(2)
    summaries['series'] = series_summary

    return summaries

def generate_excel_download(commission_df, summaries):
    """Generate Excel file with multiple sheets including summaries"""
    output = BytesIO()

    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        workbook = writer.book
        bold_format = workbook.add_format({'bold': True})
        money_format = workbook.add_format({'num_format': '#,##0.00'})
        percent_format = workbook.add_format({'num_format': '0.00%'})
        # Add right alignment format for strings
        right_align = workbook.add_format({'align': 'right'})

        # Write main commission data
        commission_df.to_excel(writer, sheet_name="Commission Details", index=False)

        # Write summaries
        # Write sheets in order
        summaries['distributor'].to_excel(writer, sheet_name="Distributor Summary")
        
        # Write Introducer Summary with breakdown
        intro_base = summaries['introducer']['base']
        intro_breakdown = summaries['introducer']['breakdown']
        
        # First write base summary to create the sheet
        intro_base.to_excel(writer, sheet_name="Introducer Summary", startrow=0)
        
        # Now we can safely get the worksheet
        worksheet = writer.sheets["Introducer Summary"]
        
        # Write detailed breakdown
        current_row = 0
        for introducer, breakdown in summaries['introducer']['breakdown'].items():
            # Write introducer name
            worksheet.write(current_row, 4, introducer, bold_format)
            current_row += 1
            
            # Write details
            details_df = breakdown['details']
            details_df.to_excel(writer, sheet_name="Introducer Summary", startrow=current_row, startcol=4, index=False)
            current_row += len(details_df) + 1
            
            # Write total
            worksheet.write(current_row, 4, "Total", bold_format)
            worksheet.write(current_row, 8, breakdown['total'], money_format)
            current_row += 2
        
        summaries['series'].to_excel(writer, sheet_name="Series Summary")

        # Format Commission Details sheet
        worksheet = writer.sheets["Commission Details"]
        worksheet.set_column('C:D', 15, money_format)  # Face Value columns
        worksheet.set_column('G:H', 15, money_format)  # Commission columns
        worksheet.set_column('J:K', 15, money_format)  # Commission columns
        worksheet.set_column('F:F', 12, percent_format)  # Distributor Rate
        worksheet.set_column('J:J', 12, percent_format)  # Introducer Rate (Adjusted column index for new 'Slab Applied' column)
        # Format Total Distributor Face Value column with both right alignment and number format
        large_value_format = workbook.add_format({
            'num_format': '#,##0',  # No decimals for large numbers
            'align': 'right'
        })
        worksheet.set_column('E:E', 20, large_value_format)  # Total Distributor Face Value column
        worksheet.set_column('F:F', 25) # Slab Applied Column


        # Format Summary sheets
        # Format Distributor Summary
        worksheet = writer.sheets["Distributor Summary"]
        worksheet.set_column('A:D', 15, money_format)  # All numeric columns

        # Format Introducer Summary
        worksheet = writer.sheets["Introducer Summary"]
        worksheet.set_column('A:A', 30)  # Introducer names
        worksheet.set_column('B:C', 15, money_format)  # Base summary numeric columns
        worksheet.set_column('E:E', 40)  # Breakdown header
        worksheet.set_column('F:Z', 15, money_format)  # Breakdown numeric columns
        
        # Format Series Summary sheet with Product Name
        worksheet = writer.sheets["Series Summary"]
        worksheet.set_column('A:A', 30)  # Product Name column
        worksheet.set_column('B:F', 15, money_format)  # Numeric columns

    return output.getvalue()

def initialize_session_state():
    """Initialize session state variables for overrides"""
    if 'commission_overrides' not in st.session_state:
        st.session_state.commission_overrides = {}  # {distributor: extra_percentage}
    if 'introducer_overrides' not in st.session_state:
        st.session_state.introducer_overrides = {}  # {introducer: extra_percentage}
    if 'slab_overrides' not in st.session_state:
        st.session_state.slab_overrides = {} # {distributor: "slab_range_string" or "ZERO"}
    if 'manual_adjustments' not in st.session_state:
        st.session_state.manual_adjustments = [] # List to store manual adjustments

def override_controls(commission_df, all_slab_ranges):
    """UI controls for extra commission and slab overrides"""
    st.markdown("### 🔧 Commission & Slab Override Controls")
    
    col1, col2 = st.columns(2)
    
    # Distributor Extra Commission Controls
    with col1:
        st.markdown("#### Distributor Extra Commission")
        distributors = sorted(commission_df['Distributor'].unique().tolist())
        if distributors:
            selected_dist_extra = st.selectbox("Select Distributor (Extra Commission)", distributors, key='comm_dist_sel')
            dist_extra = st.number_input("Additional Commission %",
                                       min_value=0.0,
                                       max_value=1.0,
                                       value=0.1,
                                       step=0.01,
                                       format="%.2f",
                                       key='dist_extra_val')
            
            if st.button("Preview Extra Commission", key="preview_dist_extra"):
                try:
                    curr_info = commission_df[commission_df['Distributor'] == selected_dist_extra]
                    if not curr_info.empty:
                        curr_rate = curr_info.iloc[0]['Distributor Rate']
                        # Need total face value for the distributor across all series for this calculation
                        dist_overall_total_fv = commission_df[
                            commission_df['Distributor'] == selected_dist_extra
                        ]['Face Value'].sum()

                        curr_rate_val = float(curr_rate.rstrip('%'))
                        new_rate = curr_rate_val + dist_extra
                        extra_amount = (dist_overall_total_fv * dist_extra / 100)
                        
                        st.info(f"""
                        **Preview for {selected_dist_extra}**
                        Total Face Value (for extra commission calc): ₹{dist_overall_total_fv:,.2f}
                        Current Base Rate (from calculated slab): {curr_rate}
                        Additional: +{dist_extra}%
                        New Effective Rate: {new_rate:.2f}%
                        Extra Commission Amount: ₹{extra_amount:,.2f} (This is a total over all transactions for this distributor)
                        """)
                    else:
                        st.warning("No data found for the selected distributor.")
                except Exception as e:
                    st.error(f"Could not generate preview: {str(e)}")
            
            if st.button("Apply Extra Commission", key="apply_dist_extra"):
                st.session_state.commission_overrides[selected_dist_extra] = dist_extra
                st.success(f"Added {dist_extra}% extra commission to {selected_dist_extra}")
                st.rerun()

    # Introducer Extra Commission Controls
    with col2:
        st.markdown("#### Introducer Extra Commission")
        # Filter out empty/direct introducers
        introducers = sorted([
            intro for intro in commission_df['Introducer'].unique()
            if not is_empty_or_direct(intro)
        ])
        if not introducers:
            st.info("No introducers found in the data.")
        else:
            selected_intro_extra = st.selectbox("Select Introducer (Extra Commission)", introducers, key='comm_intro_sel')
            intro_extra = st.number_input("Additional Commission %",
                                        min_value=0.0,
                                        max_value=1.0,
                                        value=0.1,
                                        step=0.01,
                                        format="%.2f",
                                        key='intro_extra_val')
            
            if st.button("Preview Introducer Extra Commission", key="preview_intro_extra"):
                try:
                    intro_data = commission_df[commission_df['Introducer'] == selected_intro_extra]
                    if not intro_data.empty:
                        curr_rate = intro_data.iloc[0]['Introducer Rate']
                        total_business = intro_data['Face Value'].sum()
                        
                        curr_rate_val = float(curr_rate.rstrip('%'))
                        new_rate = curr_rate_val + intro_extra
                        extra_amount = (total_business * intro_extra / 100)
                        
                        st.info(f"""
                        **Preview for {selected_intro_extra}**
                        Total Face Value (for extra commission calc): ₹{total_business:,.2f}
                        Current Base Rate (from calculated slab): {curr_rate}
                        Additional: +{intro_extra}%
                        New Effective Rate: {new_rate:.2f}%
                        Extra Commission Amount: ₹{extra_amount:,.2f} (This is a total over all transactions for this introducer)
                        """)
                    else:
                        st.warning("No data found for the selected introducer.")
                except Exception as e:
                    st.error(f"Could not generate preview: {str(e)}")
            
            if st.button("Apply Introducer Extra Commission", key="apply_intro_extra"):
                st.session_state.introducer_overrides[selected_intro_extra] = intro_extra
                st.success(f"Added {intro_extra}% extra commission to {selected_intro_extra}")
                st.rerun()

    st.markdown("---")
    # Slab Override Controls (New Section)
    st.markdown("#### Manual Slab Selection / Zero Commission Override")
    if distributors:
        selected_dist_slab = st.selectbox("Select Distributor (Slab Override)", distributors, key='slab_dist_sel')
        
        # Options for slab selection: "Auto (Default)" + all parsed slab ranges + "0% Commission"
        slab_options = ["Auto (Default)"] + all_slab_ranges + ["0% Commission"]
        
        # Set default value for the dropdown based on current override or "Auto"
        current_slab_override = st.session_state.slab_overrides.get(selected_dist_slab, "Auto (Default)")
        selected_slab_value = st.selectbox("Select Slab or Commission Override",
                                           slab_options,
                                           index=slab_options.index(current_slab_override) if current_slab_override in slab_options else 0,
                                           key='selected_slab_val')
        
        if st.button("Apply Slab Override", key="apply_slab_override"):
            if selected_slab_value == "Auto (Default)":
                if selected_dist_slab in st.session_state.slab_overrides:
                    del st.session_state.slab_overrides[selected_dist_slab]
                    st.success(f"Slab override for {selected_dist_slab} removed. Reverting to automatic calculation.")
            elif selected_slab_value == "0% Commission":
                st.session_state.slab_overrides[selected_dist_slab] = "ZERO"
                st.success(f"Applied 0% commission for {selected_dist_slab}.")
            else:
                st.session_state.slab_overrides[selected_dist_slab] = selected_slab_value
                st.success(f"Applied slab '{selected_slab_value}' for {selected_dist_slab}.")
            st.rerun()
    else:
        st.info("No distributors available to apply slab overrides.")
    
    st.markdown("---")

    # --- Manual Commission Adjustments Controls ---
    st.markdown("### ✍️ Manual Commission Adjustments")

    adjustment_type = st.selectbox(
        "Select Adjustment Type",
        ["Distributor", "Introducer", "Client"],
        key='adjustment_type_sel'
    )

    st.markdown("---")

    # UI for manual adjustments based on type
    if adjustment_type == "Distributor":
        st.markdown("#### Adjust Distributor Commission")
        distributors = sorted(commission_df['Distributor'].unique().tolist())
        if distributors:
            selected_dist_adj = st.selectbox("Select Distributor", distributors, key='manual_dist_sel')
            adjustment_amount = st.number_input("Adjustment Amount (INR)", value=0.0, step=100.0, format="%.2f", key='manual_dist_amount')
            adjustment_is_percentage = st.checkbox("Is Percentage Adjustment?", key='manual_dist_percent')
            adjustment_remarks = st.text_input("Remarks (Optional)", key='manual_dist_remarks')


            if st.button("Apply Adjustment", key="apply_manual_dist"):
                # Logic to apply manual adjustment for distributor
                st.session_state.manual_adjustments.append({
                    'type': 'Distributor',
                    'entity': selected_dist_adj,
                    'amount': adjustment_amount,
                    'is_percentage': adjustment_is_percentage,
                    'remarks': adjustment_remarks
                })
                st.success(f"Manual adjustment added for Distributor: {selected_dist_adj}")
                st.rerun()
        else:
            st.info("No distributors available for manual adjustment.")

    elif adjustment_type == "Introducer":
        st.markdown("#### Adjust Introducer Commission")
        introducers = sorted([
            intro for intro in commission_df['Introducer'].unique()
            if not is_empty_or_direct(intro)
        ])
        if introducers:
            selected_intro_adj = st.selectbox("Select Introducer", introducers, key='manual_intro_sel')
            adjustment_amount = st.number_input("Adjustment Amount (INR)", value=0.0, step=100.0, format="%.2f", key='manual_intro_amount')
            adjustment_is_percentage = st.checkbox("Is Percentage Adjustment?", key='manual_intro_percent')
            adjustment_remarks = st.text_input("Remarks (Optional)", key='manual_intro_remarks')

            if st.button("Apply Adjustment", key="apply_manual_intro"):
                # Logic to apply manual adjustment for introducer
                st.session_state.manual_adjustments.append({
                    'type': 'Introducer',
                    'entity': selected_intro_adj,
                    'amount': adjustment_amount,
                    'is_percentage': adjustment_is_percentage,
                    'remarks': adjustment_remarks
                })
                st.success(f"Manual adjustment added for Introducer: {selected_intro_adj}")
                st.rerun()
        else:
            st.info("No introducers available for manual adjustment.")

    elif adjustment_type == "Client":
        st.markdown("#### Adjust Client Commission")
        clients = sorted(commission_df['Client'].unique().tolist())
        if clients:
            selected_client_adj = st.selectbox("Select Client", clients, key='manual_client_sel')
            adjustment_amount = st.number_input("Adjustment Amount (INR)", value=0.0, step=100.0, format="%.2f", key='manual_client_amount')
            adjustment_is_percentage = st.checkbox("Is Percentage Adjustment?", key='manual_client_percent')
            adjustment_remarks = st.text_input("Remarks (Optional)", key='manual_client_remarks')

            # Add optional product filter for client adjustments
            product_name_col_mapping = st.session_state['column_mappings'].get('product_name')
            if product_name_col_mapping:
                 product_names = sorted(commission_df['Product Name'].unique().tolist())
                 selected_product_adj = st.selectbox("Filter by Product (Optional)", ["All Products"] + product_names, key='manual_client_product_filter')
            else:
                 selected_product_adj = "All Products" # Default if no product column

            if st.button("Apply Adjustment", key="apply_manual_client"):
                # Logic to apply manual adjustment for client
                 adjustment_entry = {
                    'type': 'Client',
                    'entity': selected_client_adj,
                    'amount': adjustment_amount,
                    'is_percentage': adjustment_is_percentage,
                    'remarks': adjustment_remarks
                 }
                 if selected_product_adj != "All Products":
                     adjustment_entry['product_name'] = selected_product_adj # Add product filter if selected

                 st.session_state.manual_adjustments.append(adjustment_entry)
                 st.success(f"Manual adjustment added for Client: {selected_client_adj}")
                 st.rerun()
        else:
            st.info("No clients available for manual adjustment.")

    st.markdown("---")

    # Show active overrides and adjustments
    if st.session_state.commission_overrides or st.session_state.introducer_overrides or st.session_state.slab_overrides or st.session_state.manual_adjustments:
        st.markdown("### 📋 Active Overrides & Manual Adjustments")

        # Show active overrides
        if st.session_state.commission_overrides or st.session_state.introducer_overrides or st.session_state.slab_overrides:
            st.markdown("#### Commission & Slab Overrides")

            if st.session_state.commission_overrides:
                st.markdown("##### Distributor Extra Commission Overrides")
                for dist, extra in st.session_state.commission_overrides.items():
                    st.text(f"• {dist}: +{extra:.2f}%")

            if st.session_state.introducer_overrides:
                st.markdown("##### Introducer Extra Commission Overrides")
                for intro, extra in st.session_state.introducer_overrides.items():
                    st.text(f"• {intro}: +{extra:.2f}%")

            if st.session_state.slab_overrides:
                st.markdown("##### Manual Slab / Zero Commission Overrides")
                for dist, slab_val in st.session_state.slab_overrides.items():
                    display_val = "0% Commission" if slab_val == "ZERO" else slab_val
                    st.text(f"• {dist}: Forced Slab/Commission: {display_val}")

        # Show active manual adjustments
        if st.session_state.manual_adjustments:
            st.markdown("#### Manual Adjustments")
            for adj in st.session_state.manual_adjustments:
                adj_text = f"• {adj['type']}: {adj['entity']} - {'+' if adj['amount'] >= 0 else ''}{adj['amount']}{'%' if adj['is_percentage'] else ' INR'}"
                if adj.get('product_name'):
                    adj_text += f" (Product: {adj['product_name']})"
                if adj.get('remarks'):
                    adj_text += f" (Remarks: {adj['remarks']})"
                st.text(adj_text)


        if st.button("Reset All Overrides & Adjustments", key="reset_all"):
            st.session_state.commission_overrides = {}
            st.session_state.introducer_overrides = {}
            st.session_state.slab_overrides = {}
            st.session_state.manual_adjustments = []
            st.success("All overrides and manual adjustments have been reset")
            st.rerun()

def main():
    """Main application logic"""
    st.markdown("### 📁 Upload Data")
    uploaded_file = st.file_uploader("Upload your Excel file with Input and Slab sheets", type=["xlsx"])
    
    # Initialize session state
    initialize_session_state()

    if not uploaded_file:
        return

    try:
        xl = pd.ExcelFile(uploaded_file)

        # Validate Excel structure
        is_valid, message = validate_excel_structure(xl)
        if not is_valid:
            st.error("❌ Invalid Excel structure. Please ensure your file has both Input and Slab sheets with the required columns.")
            st.stop()

        # Read sheets with proper data type handling
        input_df = xl.parse(xl.sheet_names[0], dtype=str)  # Read all as strings initially
        slab_df = xl.parse(xl.sheet_names[1], header=None, dtype=str)

        # Convert face value to numeric after reading
        def clean_numeric(val):
            try:
                # Handle special cases first
                val_str = str(val)
                if val_str == '[object Object]' or not val_str.strip():
                    return 0.0

                # Use the same Indian number parser for consistency
                cleaned_val = parse_indian_number(val_str)
                if cleaned_val == 0 and any(c.isdigit() for c in val_str):
                    # Fallback to simple numeric parsing if indian format fails
                    cleaned = re.sub(r'[^\d.-]', '', val_str)
                    return float(cleaned) if cleaned else 0.0
                return cleaned_val

            except Exception as e:
                return 0.0

        def clean_series(val):
            try:
                # Extract series number from formats like "Series 129 Tranche 1"
                val_str = str(val)
                match = re.search(r'Series\s*(\d+)', val_str)
                return f"Series {match.group(1)}" if match else val_str
            except:
                return val_str

        # Clean numeric columns after mapping is established
        if 'column_mappings' in st.session_state:
            face_value_col = st.session_state['column_mappings']['face_value']
            series_col = st.session_state['column_mappings']['series_no']

            # Clean face values and series numbers (apply only once)
            input_df[face_value_col] = input_df[face_value_col].apply(clean_numeric)
            input_df[series_col] = input_df[series_col].apply(clean_series)

            # Remove invalid rows
            input_df = input_df[
                (input_df[series_col].notna()) &
                (input_df[series_col].str.contains('Series', case=False, na=False)) &
                (input_df[face_value_col] > 0)
            ]

        # Convert slab data to string to avoid type issues
        slab_df = slab_df.astype(str)

        # Process data and calculate commissions
        slab_info, all_slab_ranges = parse_slab_matrix(slab_df) # Get all_slab_ranges here
        commission_df = calculate_commissions(input_df, slab_info)
        summaries = generate_summary(commission_df)

        # Add override controls before displaying results
        st.markdown("---")
        override_controls(commission_df, all_slab_ranges) # Pass all_slab_ranges to controls
        st.markdown("---")

        # Display results in tabs
        tab1, tab2, tab3, tab4 = st.tabs([
            "📊 Details",
            "🏢 Distributor",
            "👥 Introducer",
            "📈 Series"
        ])

        with tab1:
            st.subheader("📋 Commission Details")
            st.dataframe(
                commission_df,
                use_container_width=True,
                hide_index=True
            )

        with tab2:
            st.subheader("💼 Distributor Summary")
            st.dataframe(summaries['distributor'], use_container_width=True)

        with tab3:
            st.subheader("🤝 Introducer Summary")
            
            # Display base summary
            st.markdown("##### Overall Summary")
            st.dataframe(summaries['introducer']['base'], use_container_width=True)
            
            # Display breakdown
            st.markdown("##### Commission Breakdown by Distributor and Series")
            
            # Display detailed breakdown for each introducer
            for introducer, breakdown in summaries['introducer']['breakdown'].items():
                st.markdown(f"**{introducer}**")
                details_df = breakdown['details']
                
                # Format the display columns
                display_df = details_df.copy()
                display_df['Face Value'] = display_df['Face Value'].apply(lambda x: f"₹{x:,.2f}")
                display_df = display_df.rename(columns={'Introducer Rate': 'Rate'})
                
                st.dataframe(
                    display_df[['Distributor', 'Face Value', 'Series', 'Rate', 'Introducer Commission']],
                    use_container_width=True,
                    hide_index=True
                )
                st.markdown(f"**Total Commission: ₹{breakdown['total']:,.2f}**")
                st.markdown("---")

        with tab4:
            st.subheader("📊 Series Summary")
            st.dataframe(
                summaries['series'],
                use_container_width=True
            )

        # Note about active overrides
        if st.session_state.commission_overrides or st.session_state.introducer_overrides or st.session_state.slab_overrides:
            st.info("💡 Active commission overrides (Distributor, Introducer, & Slab) will be reflected in the downloaded report.")

        # Generate download buttons
        col1, col2 = st.columns(2)
        # Download button
        excel_bytes = generate_excel_download(commission_df, summaries)
        st.markdown("---")
        st.markdown("### � Download Report")
        st.download_button(
            "💾 Download Complete Report",
            data=excel_bytes,
            file_name=f"commission_report_{datetime.now().strftime('%Y%m%d')}.xlsx",
            mime="application/vnd.ms-excel",
        )

    except Exception as e:
        st.error(f"Error reading the Excel file: {str(e)}")
        import traceback
        st.error(f"Detailed error: {traceback.format_exc()}")
        return

if __name__ == "__main__":
    main()
