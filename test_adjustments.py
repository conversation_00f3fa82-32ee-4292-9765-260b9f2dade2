#!/usr/bin/env python3
"""
Test script to verify commission adjustment logic works correctly.
This script tests the key scenarios mentioned in the requirements.
"""

import pandas as pd
import streamlit as st
import sys
import os

# Add the current directory to the path so we can import from app.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import calculate_commissions, generate_summary, initialize_session_state, normalize_company_name

def create_test_data():
    """Create test data to verify adjustment logic"""
    # Create test input data
    test_data = {
        'Client Name ': ['Client A', 'Client B', 'Client C', 'Client D'],
        'Series No ': ['Series 129', 'Series 129', 'Series 123', 'Series 129'],
        'Face Value ': [500000, 300000, 100000, 200000],
        'Distributor': ['ABC Ltd', 'ABC Ltd', 'XYZ Pvt Ltd', 'ABC Ltd'],  # ABC Ltd appears multiple times
        'Introducer': ['Lighthouse Consultancy', 'Lighthouse Consultancy', 'Direct', 'Different Intro'],
        'Product Name': ['Test Product 1', 'Test Product 2', 'Test Product 3', 'Test Product 4']
    }
    
    input_df = pd.DataFrame(test_data)
    
    # Create test slab data (simplified)
    slab_data = {
        0: ['', 'Series 129', 'Series 123'],
        1: ['', '', ''],
        2: ['', 'Distributor', 'Distributor'],
        3: ['', 'Introducer', 'Introducer'],
        4: ['Upto 5,00,000', '1.5', '1.0'],
        5: ['5,00,001 - 10,00,000', '1.75', '1.25'],
        6: ['10,00,001 and above', '2.0', '1.5']
    }
    
    slab_df = pd.DataFrame(slab_data)
    
    return input_df, slab_df

def setup_test_session_state():
    """Setup session state for testing"""
    # Initialize session state
    initialize_session_state()
    
    # Set up column mappings
    st.session_state['column_mappings'] = {
        'client_name': 'Client Name ',
        'series_no': 'Series No ',
        'face_value': 'Face Value ',
        'distributor': 'Distributor',
        'introducer': 'Introducer',
        'product_name': 'Product Name'
    }
    
    # Clear any existing adjustments
    st.session_state.manual_adjustments = []
    st.session_state.commission_overrides = {}
    st.session_state.introducer_overrides = {}
    st.session_state.slab_overrides = {}

def test_independent_adjustments():
    """Test that Distributor, Introducer, and Client adjustments work independently"""
    print("=== Testing Independent Adjustments ===")
    
    input_df, slab_df = create_test_data()
    setup_test_session_state()
    
    # Add manual adjustments for different types
    st.session_state.manual_adjustments = [
        {
            'type': 'Distributor',
            'entity': 'ABC Ltd',
            'amount': -500,  # Negative adjustment
            'is_percentage': False,
            'remarks': 'Distributor penalty'
        },
        {
            'type': 'Introducer', 
            'entity': 'Lighthouse Consultancy',
            'amount': 200,  # Positive adjustment
            'is_percentage': False,
            'remarks': 'Introducer bonus'
        },
        {
            'type': 'Client',
            'entity': 'Client A',
            'amount': 100,  # Client adjustment
            'is_percentage': False,
            'remarks': 'Client special adjustment'
        }
    ]
    
    # Parse slab info (simplified for testing)
    slab_info = {
        '129': [(0, 500000, 0.015, 0.01), (500001, 1000000, 0.0175, 0.0125), (1000001, float('inf'), 0.02, 0.015)],
        '123': [(0, 500000, 0.015, 0.01), (500001, 1000000, 0.0175, 0.0125), (1000001, float('inf'), 0.02, 0.015)]
    }
    
    # Calculate commissions
    commission_df = calculate_commissions(input_df, slab_info)
    
    print("Commission DataFrame:")
    print(commission_df[['Client', 'Distributor', 'Introducer', 'Face Value', 
                        'Distributor Commission', 'Introducer Commission', 'Total Commission',
                        'Distributor Adjustment Amount', 'Introducer Adjustment Amount', 'Client Adjustment Amount']].to_string())
    
    # Verify adjustments are applied correctly
    abc_rows = commission_df[commission_df['Distributor'] == 'ABC Ltd']
    lighthouse_rows = commission_df[commission_df['Introducer'] == 'Lighthouse Consultancy']
    client_a_rows = commission_df[commission_df['Client'] == 'Client A']
    
    print(f"\nABC Ltd rows with distributor adjustments: {len(abc_rows[abc_rows['Distributor Adjustment Amount'] != 0])}")
    print(f"Lighthouse Consultancy rows with introducer adjustments: {len(lighthouse_rows[lighthouse_rows['Introducer Adjustment Amount'] != 0])}")
    print(f"Client A rows with client adjustments: {len(client_a_rows[client_a_rows['Client Adjustment Amount'] != 0])}")
    
    # Test that adjustments are only applied once per entity
    assert len(abc_rows[abc_rows['Distributor Adjustment Amount'] != 0]) == 1, "Distributor adjustment should only be applied once"
    assert len(lighthouse_rows[lighthouse_rows['Introducer Adjustment Amount'] != 0]) == 1, "Introducer adjustment should only be applied once"
    assert len(client_a_rows[client_a_rows['Client Adjustment Amount'] != 0]) == 1, "Client adjustment should only be applied once"
    
    print("✅ Independent adjustments test passed!")
    
    return commission_df

def test_summary_totals(commission_df):
    """Test that summaries reflect adjusted totals"""
    print("\n=== Testing Summary Totals ===")
    
    summaries = generate_summary(commission_df)
    
    print("Distributor Summary:")
    print(summaries['distributor'].to_string())
    
    print("\nIntroducer Summary:")
    print(summaries['introducer']['base'].to_string())
    
    print("\nSeries Summary:")
    print(summaries['series'].to_string())
    
    # Verify that ABC Ltd's total includes the adjustment
    abc_total = summaries['distributor'].loc['ABC Ltd', 'Distributor Commission']
    abc_detail_total = commission_df[commission_df['Distributor'] == 'ABC Ltd']['Distributor Commission'].sum()
    
    print(f"\nABC Ltd total from summary: {abc_total}")
    print(f"ABC Ltd total from detail: {abc_detail_total}")
    
    assert abs(abc_total - abc_detail_total) < 0.01, "Distributor summary should match detail totals"
    
    # Verify that Lighthouse Consultancy's total includes the adjustment
    lighthouse_total = summaries['introducer']['base'].loc['Lighthouse Consultancy', 'Introducer Commission']
    lighthouse_detail_total = commission_df[commission_df['Introducer'] == 'Lighthouse Consultancy']['Introducer Commission'].sum()
    
    print(f"Lighthouse Consultancy total from summary: {lighthouse_total}")
    print(f"Lighthouse Consultancy total from detail: {lighthouse_detail_total}")
    
    assert abs(lighthouse_total - lighthouse_detail_total) < 0.01, "Introducer summary should match detail totals"
    
    print("✅ Summary totals test passed!")

def test_multiple_calculations():
    """Test that adjustments work correctly across multiple calculation runs"""
    print("\n=== Testing Multiple Calculations ===")
    
    input_df, slab_df = create_test_data()
    setup_test_session_state()
    
    # Add an adjustment
    st.session_state.manual_adjustments = [
        {
            'type': 'Distributor',
            'entity': 'ABC Ltd',
            'amount': -300,
            'is_percentage': False,
            'remarks': 'Test adjustment'
        }
    ]
    
    slab_info = {
        '129': [(0, 500000, 0.015, 0.01), (500001, 1000000, 0.0175, 0.0125), (1000001, float('inf'), 0.02, 0.015)],
        '123': [(0, 500000, 0.015, 0.01), (500001, 1000000, 0.0175, 0.0125), (1000001, float('inf'), 0.02, 0.015)]
    }
    
    # Run calculation twice
    commission_df1 = calculate_commissions(input_df, slab_info)
    commission_df2 = calculate_commissions(input_df, slab_info)
    
    # Check that adjustments are applied in both runs
    abc_adj_count1 = len(commission_df1[commission_df1['Distributor Adjustment Amount'] != 0])
    abc_adj_count2 = len(commission_df2[commission_df2['Distributor Adjustment Amount'] != 0])
    
    print(f"First calculation - ABC Ltd adjustment count: {abc_adj_count1}")
    print(f"Second calculation - ABC Ltd adjustment count: {abc_adj_count2}")
    
    assert abc_adj_count1 == 1, "First calculation should have exactly one adjustment"
    assert abc_adj_count2 == 1, "Second calculation should have exactly one adjustment"
    
    print("✅ Multiple calculations test passed!")

if __name__ == "__main__":
    print("Starting Commission Adjustment Tests...")
    
    try:
        # Run tests
        commission_df = test_independent_adjustments()
        test_summary_totals(commission_df)
        test_multiple_calculations()
        
        print("\n🎉 All tests passed! Commission adjustment logic is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
