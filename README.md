# Commission Calculator

A comprehensive Streamlit-based commission calculation application for processing distributor, introducer, and client commissions with manual adjustment capabilities.

## Features

- **Excel File Processing**: Upload and process Excel files with Input and Slab data
- **Commission Calculation**: Automatic calculation based on configurable slab structures
- **Manual Adjustments**: Apply percentage or fixed amount adjustments to distributors, introducers, and clients
- **Commission Overrides**: Override commission rates for specific distributors and introducers
- **Slab Overrides**: Override slab rates for specific series
- **Multiple Summary Reports**: Generate distributor, introducer, and series summaries
- **Excel Export**: Download comprehensive reports with all calculations and summaries

## Requirements

- Python 3.8 or higher
- Required packages (automatically installed via requirements.txt):
  - pandas>=2.0.0
  - openpyxl>=3.1.0
  - streamlit>=1.22.0
  - xlsxwriter>=3.1.0
  - numpy>=1.24.0

## Installation & Setup

### Option 1: Using the Batch File (Windows)
1. Double-click `run_calculator.bat`
2. The script will automatically install dependencies and start the application

### Option 2: Manual Installation
1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the application:
   ```bash
   streamlit run app.py
   ```

3. Open your browser and navigate to the displayed URL (typically http://localhost:8501)

## Excel File Format

### Input Sheet Requirements
Your Excel file must contain an "Input" sheet with the following columns:
- Client Name
- Series No
- Face Value
- Distributor
- Introducer
- Product Name

### Slab Sheet Requirements
Your Excel file must contain a "Slab" sheet with:
- Series information in the first row
- Commission rate structure for different face value ranges
- Separate columns for Distributor and Introducer rates

## Usage Guide

### 1. Upload Excel File
- Click "Choose an Excel file" and select your input file
- The application will validate the file structure automatically

### 2. Configure Column Mappings
- Map your Excel columns to the required fields
- The application will auto-detect common column names

### 3. Apply Overrides (Optional)
- **Commission Overrides**: Set custom commission rates for specific distributors/introducers
- **Slab Overrides**: Override slab rates for specific series
- **Manual Adjustments**: Apply fixed amounts or percentages to entities

### 4. Calculate Commissions
- Click "Calculate Commissions" to process the data
- Review the results in the detailed commission table

### 5. Download Reports
- Click "Download Excel Report" to get a comprehensive report with:
  - Commission Details
  - Distributor Summary
  - Introducer Summary
  - Series Summary

## Manual Adjustments

The application supports three types of manual adjustments:

### Distributor Adjustments
- Apply to all transactions for a specific distributor
- Can be positive (bonus) or negative (penalty)
- Supports both percentage and fixed amount adjustments

### Introducer Adjustments
- Apply to all transactions for a specific introducer
- Can be positive (bonus) or negative (penalty)
- Supports both percentage and fixed amount adjustments

### Client Adjustments
- Apply to specific client transactions
- Optional product filtering for targeted adjustments
- Supports both percentage and fixed amount adjustments

## Key Features

### Adjustment Logic
- Adjustments are applied only once per entity per calculation
- Independent tracking for distributors, introducers, and clients
- Adjustments are reflected in both detailed data and summary reports

### Data Validation
- Comprehensive validation of Excel file structure
- Error handling for missing or invalid data
- Clear error messages and guidance

### Export Capabilities
- Multi-sheet Excel reports with formatting
- Separate summaries for different entity types
- Detailed breakdown of all calculations

## Troubleshooting

### Common Issues

1. **File Upload Errors**
   - Ensure your Excel file has both "Input" and "Slab" sheets
   - Check that required columns are present
   - Verify file is not corrupted or password-protected

2. **Calculation Errors**
   - Verify slab structure is correctly formatted
   - Check for missing or invalid face values
   - Ensure series numbers match between Input and Slab sheets

3. **Adjustment Issues**
   - Adjustments are applied only once per entity
   - Use exact entity names as they appear in the data
   - Check adjustment amounts and percentages are valid

### Support
For technical support or feature requests, please refer to the application documentation or contact the development team.

## Version History

- **Latest Version**: Includes comprehensive manual adjustment system with independent tracking
- **Key Improvements**: 
  - Fixed adjustment tracking across multiple calculations
  - Enhanced summary report accuracy
  - Improved error handling and validation

## License

This application is proprietary software developed for commission calculation purposes.
